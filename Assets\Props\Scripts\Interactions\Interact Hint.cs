using UnityEngine;
using UnityEngine.InputSystem;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

[System.Serializable]
public class InteractHintData
{
    [Header("Basic Text Display")]
    [Tooltip("Simple text for basic interaction hints. Used for static display.")]
    public string InteractionText;

    [<PERSON><PERSON>("Advanced Text Display")]
    [Tooltip("Complex text document for animated/formatted interaction hints. Takes priority over InteractionText if set.")]
    public VisualTextDocument InteractionDocument;

    [<PERSON><PERSON>("Display Settings")]
    [Toolt<PERSON>("If true, uses animated text display with typewriter effects. If false, uses static text for better performance.")]
    public bool UseAnimatedText = false;

    [Tooltip("If true, stops any current text animation before showing new text. Recommended for frequently changing interactions.")]
    public bool StopPreviousAnimation = true;

    [Header("Visual")]
    [Tooltip("Sprite to display on the crosshair.")]
    public Sprite InteractionSprite;

    /// <summary>
    /// Gets the text to display, prioritizing document over simple text.
    /// </summary>
    public string GetDisplayText()
    {
        if (InteractionDocument != null && !string.IsNullOrEmpty(InteractionDocument.fullText))
        {
            return InteractionDocument.fullText;
        }
        return InteractionText ?? string.Empty;
    }

    /// <summary>
    /// Checks if this hint has any text content to display.
    /// </summary>
    public bool HasTextContent()
    {
        return !string.IsNullOrEmpty(GetDisplayText());
    }

    /// <summary>
    /// Determines if animated display should be used based on settings and content.
    /// </summary>
    public bool ShouldUseAnimatedDisplay()
    {
        return UseAnimatedText && (InteractionDocument != null || !string.IsNullOrEmpty(InteractionText));
    }
}


