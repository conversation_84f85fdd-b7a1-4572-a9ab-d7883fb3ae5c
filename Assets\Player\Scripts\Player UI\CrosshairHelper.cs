using UnityEngine;

/// <summary>
/// Classe utilitária para facilitar a configuração de crosshairs com suporte ao sistema de texto animado
/// </summary>
public static class CrosshairHelper
{
    #region Basic Hint Creation (Legacy Support)

    /// <summary>
    /// Cria um InteractHintData básico com texto estático
    /// </summary>
    public static InteractHintData CreateBasicHint(string text)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = null,
            UseAnimatedText = false,
            StopPreviousAnimation = true
        };
    }

    /// <summary>
    /// Cria um InteractHintData completo com texto e sprite estáticos
    /// </summary>
    public static InteractHintData CreateCompleteHint(string text, Sprite sprite)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = sprite,
            UseAnimatedText = false,
            StopPreviousAnimation = true
        };
    }

    #endregion

    #region Enhanced Hint Creation

    /// <summary>
    /// Cria um InteractHintData com texto animado
    /// </summary>
    public static InteractHintData CreateAnimatedHint(string text, Sprite sprite = null)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = sprite,
            UseAnimatedText = true,
            StopPreviousAnimation = true
        };
    }

    /// <summary>
    /// Cria um InteractHintData com documento de texto complexo
    /// </summary>
    public static InteractHintData CreateDocumentHint(VisualTextDocument document, Sprite sprite = null)
    {
        return new InteractHintData
        {
            InteractionDocument = document,
            InteractionSprite = sprite,
            UseAnimatedText = true,
            StopPreviousAnimation = true
        };
    }

    /// <summary>
    /// Cria um InteractHintData com configurações personalizadas
    /// </summary>
    public static InteractHintData CreateCustomHint(string text, Sprite sprite, bool useAnimated, bool stopPrevious = true)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = sprite,
            UseAnimatedText = useAnimated,
            StopPreviousAnimation = stopPrevious
        };
    }

    #endregion

    #region Object Configuration

    /// <summary>
    /// Configura um InteractHint para um objeto interagível (método legado)
    /// </summary>
    public static void SetupInteractHint(InteractableObject obj, string text, Sprite sprite = null, bool hasCondition = false)
    {
        SetupInteractHint(obj, text, sprite, hasCondition, false);
    }

    /// <summary>
    /// Configura um InteractHint para um objeto interagível com suporte a texto animado
    /// </summary>
    public static void SetupInteractHint(InteractableObject obj, string text, Sprite sprite = null, bool hasCondition = false, bool useAnimated = false)
    {
        if (obj.interactHint == null)
        {
            obj.interactHint = new InteractHint();
        }

        obj.interactHint.interactionHint = useAnimated ? CreateAnimatedHint(text, sprite) : CreateCompleteHint(text, sprite);
        obj.interactHint.HasCondition = hasCondition;
    }

    /// <summary>
    /// Configura um InteractHint com documento de texto complexo
    /// </summary>
    public static void SetupDocumentHint(InteractableObject obj, VisualTextDocument document, Sprite sprite = null, bool hasCondition = false)
    {
        if (obj.interactHint == null)
        {
            obj.interactHint = new InteractHint();
        }

        obj.interactHint.interactionHint = CreateDocumentHint(document, sprite);
        obj.interactHint.HasCondition = hasCondition;
    }

    #endregion

    #region Runtime Helpers

    /// <summary>
    /// Atualiza o texto de um objeto interagível em runtime
    /// </summary>
    public static void UpdateInteractionText(InteractableObject obj, string newText)
    {
        if (obj?.interactHint?.interactionHint != null)
        {
            obj.interactHint.interactionHint.InteractionText = newText;
        }
    }

    /// <summary>
    /// Atualiza o documento de um objeto interagível em runtime
    /// </summary>
    public static void UpdateInteractionDocument(InteractableObject obj, VisualTextDocument newDocument)
    {
        if (obj?.interactHint?.interactionHint != null)
        {
            obj.interactHint.interactionHint.InteractionDocument = newDocument;
            obj.interactHint.interactionHint.UseAnimatedText = true;
        }
    }

    /// <summary>
    /// Alterna entre texto estático e animado em runtime
    /// </summary>
    public static void ToggleAnimatedText(InteractableObject obj, bool useAnimated)
    {
        if (obj?.interactHint?.interactionHint != null)
        {
            obj.interactHint.interactionHint.UseAnimatedText = useAnimated;
        }
    }

    #endregion
}