using UnityEngine;
using System;
using System.Collections.Generic;

/// <summary>
/// Representa uma "Frase" ou "Parágrafo", que é um conjunto de sentenças
/// exibidas em sequência antes de uma possível pausa ou limpeza de tela.
/// </summary>
[Serializable]
public class PhraseData
{
    [Tooltip("A lista de sentenças que compõem esta frase.")]
    public List<SentenceDisplayData> Sentences = new List<SentenceDisplayData>();

    [Tooltip("O atraso após a conclusão desta frase antes de prosseguir para a próxima ou aguardar input.")]
    public float DelayAfterPhrase = 0f;

    [Header("Controle de Avanço")]
    [Tooltip("Se verdadeiro, esta frase só pode avançar para a próxima quando um skip for chamado.")]
    public bool RequireSkipToAdvance = false;

    // Campo para armazenar a representação em string de todas as sentenças desta frase.
    // [NonSerialized] para evitar que o Unity tente serializar este campo, pois ele é gerado dinamicamente.
    [NonSerialized]
    public string FullPhraseText;

    [Tooltip("O texto bruto original para esta frase, permitindo o re-parsing individual.")]
    [TextArea(3, 5)]
    public string OriginalText;

    public PhraseData() { }
}