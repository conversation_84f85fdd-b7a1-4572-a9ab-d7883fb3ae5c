using UnityEngine;
using TMPro;

/// <summary>
/// Estrutura de dados para armazenar informações temporárias
/// e específicas da animação de uma sentença.
/// </summary>
public class SentenceInfo
{
    public int StartIndex;
    public int EndIndex;

    public SentenceDisplayData originalSentence;

    // Armazena os vértices originais da sentença antes de qualquer modificação do efeito.
    // É um array de arrays de Vector3, onde o primeiro índice se refere ao submesh (material),
    // e o segundo ao vértice.
    public Vector3[][] originalVertices;

    // Armazena as cores originais da sentença antes de qualquer modificação do efeito.
    public Color32[][] originalColors;

    public SentenceInfo(TMP_TextInfo textInfo)
    {
        // Inicializa o array de arrays de vértices com base no número de submeshes.
        originalVertices = new Vector3[textInfo.meshInfo.Length][];
        originalColors = new Color32[textInfo.meshInfo.Length][];

        // Clona os vértices originais de cada submesh.
        for (int i = 0; i < textInfo.meshInfo.Length; i++)
        {
            if (textInfo.meshInfo[i].vertices != null)
            {
                originalVertices[i] = (Vector3[])textInfo.meshInfo[i].vertices.Clone();
            }
            else
            {
                originalVertices[i] = new Vector3[0]; // Garante que não é nulo
            }

            if (textInfo.meshInfo[i].colors32 != null)
            {
                originalColors[i] = (Color32[])textInfo.meshInfo[i].colors32.Clone();
            }
            else
            {
                originalColors[i] = new Color32[0];
            }
        }
    }

    /// <summary>
    /// Reseta os vértices do TextMeshProUGUI para os valores originais armazenados.
    /// </summary>
    public void RestoreVertices(TextMeshProUGUI textComponent)
    {
        if (originalVertices == null || textComponent == null || textComponent.textInfo == null) return;

        var textInfo = textComponent.textInfo;

        for (int i = 0; i < textInfo.meshInfo.Length && i < originalVertices.Length; i++)
        {
            if (originalVertices.Length > i && originalVertices[i] != null &&
                textInfo.meshInfo.Length > i && textInfo.meshInfo[i].vertices != null)
            {
                originalVertices[i].CopyTo(textInfo.meshInfo[i].vertices, 0);
            }
        }
        textComponent.UpdateVertexData(TMP_VertexDataUpdateFlags.Vertices);
    }

    /// <summary>
    /// Reseta as cores dos vértices do TextMeshProUGUI para os valores originais armazenados.
    /// </summary>
    public void RestoreColors(TextMeshProUGUI textComponent)
    {
        if (originalColors == null || textComponent == null || textComponent.textInfo == null) return;

        var textInfo = textComponent.textInfo;

        for (int i = 0; i < textInfo.meshInfo.Length && i < originalColors.Length; i++)
        {
            if (originalColors[i] != null && textInfo.meshInfo[i].colors32 != null)
            {
                originalColors[i].CopyTo(textInfo.meshInfo[i].colors32, 0);
            }
        }
        textComponent.UpdateVertexData(TMP_VertexDataUpdateFlags.Colors32);
    }
}