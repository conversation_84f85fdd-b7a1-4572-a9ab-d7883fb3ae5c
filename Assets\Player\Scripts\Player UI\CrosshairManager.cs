using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class CrosshairManager : MonoBehaviour
{
    [<PERSON><PERSON>("Crosshair Visual")]
    public Image CrosshairImage;
    public Sprite DefaultSprite;
    public Sprite InteractableSprite;
    
    [<PERSON>er("Crosshair Text")]
    [Tooltip("Static text component for simple, high-performance text display.")]
    public TextMeshProUGUI CrosshairText;
    public GameObject CrosshairTextBackground;

    [<PERSON><PERSON>("Animated Text Display")]
    [Tooltip("VisualTextDisplayer for animated text with typewriter effects and formatting.")]
    public VisualTextDisplayer animatedTextDisplayer;

    [<PERSON><PERSON>("Animation Settings")]
    public float transitionSpeed = 5f;

    [<PERSON><PERSON>("Performance Settings")]
    [Tooltip("If true, prefers static text display for better performance. Animated text only used when explicitly requested.")]
    public bool preferStaticText = true;

    [Tooltip("Maximum frequency (in seconds) for text updates to prevent performance issues.")]
    public float textUpdateCooldown = 0.1f;

    private bool isShowingInteractable = false;
    private float lastTextUpdateTime = 0f;
    private InteractHintData currentHint = null;

    void Start()
    {
        SetDefaultCrosshair();
    }
    
    public void SetDefaultCrosshair()
    {
        // Reset crosshair sprite
        if (CrosshairImage != null && DefaultSprite != null)
        {
            CrosshairImage.sprite = DefaultSprite;
        }

        // Hide static text
        if (CrosshairText != null)
        {
            CrosshairText.text = "";
            CrosshairText.gameObject.SetActive(false);
        }

        // Hide text background
        if (CrosshairTextBackground != null)
        {
            CrosshairTextBackground.SetActive(false);
        }

        // Stop animated text display
        if (animatedTextDisplayer != null)
        {
            animatedTextDisplayer.StopDisplay();
        }

        // Reset state
        isShowingInteractable = false;
        currentHint = null;
    }
    
    public void SetInteractableCrosshair(InteractHintData hint)
    {
        // Performance check: avoid frequent updates
        if (Time.time - lastTextUpdateTime < textUpdateCooldown && hint == currentHint)
        {
            return;
        }

        lastTextUpdateTime = Time.time;
        currentHint = hint;

        if (hint == null)
        {
            SetDefaultCrosshair();
            return;
        }

        // Update crosshair sprite
        UpdateCrosshairSprite(hint);

        // Handle text display based on hint configuration
        if (hint.HasTextContent())
        {
            if (hint.ShouldUseAnimatedDisplay() && !preferStaticText)
            {
                DisplayAnimatedText(hint);
            }
            else
            {
                DisplayStaticText(hint);
            }
        }
        else
        {
            HideAllText();
        }

        isShowingInteractable = true;
    }
    
    public bool IsShowingInteractable()
    {
        return isShowingInteractable;
    }

    #region Helper Methods

    /// <summary>
    /// Updates the crosshair sprite based on the hint data.
    /// </summary>
    private void UpdateCrosshairSprite(InteractHintData hint)
    {
        if (CrosshairImage != null)
        {
            Sprite spriteToUse = hint.InteractionSprite != null ? hint.InteractionSprite : InteractableSprite;
            if (spriteToUse != null)
            {
                CrosshairImage.sprite = spriteToUse;
            }
        }
    }

    /// <summary>
    /// Displays text using the static TextMeshProUGUI component for better performance.
    /// </summary>
    private void DisplayStaticText(InteractHintData hint)
    {
        // Stop any animated text first
        if (animatedTextDisplayer != null && hint.StopPreviousAnimation)
        {
            animatedTextDisplayer.StopDisplay();
        }

        // Show static text
        if (CrosshairText != null)
        {
            CrosshairText.text = hint.GetDisplayText();
            CrosshairText.gameObject.SetActive(true);
        }

        // Show background
        if (CrosshairTextBackground != null)
        {
            CrosshairTextBackground.SetActive(true);
        }
    }

    /// <summary>
    /// Displays text using the VisualTextDisplayer for animated effects.
    /// </summary>
    private void DisplayAnimatedText(InteractHintData hint)
    {
        // Hide static text first
        if (CrosshairText != null)
        {
            CrosshairText.gameObject.SetActive(false);
        }

        // Show background for animated text too
        if (CrosshairTextBackground != null)
        {
            CrosshairTextBackground.SetActive(true);
        }

        // Display animated text
        if (animatedTextDisplayer != null)
        {
            if (hint.StopPreviousAnimation)
            {
                animatedTextDisplayer.StopDisplay();
            }

            if (hint.InteractionDocument != null)
            {
                // Use complex document for advanced formatting
                animatedTextDisplayer.Display(hint.InteractionDocument);
            }
            else if (!string.IsNullOrEmpty(hint.InteractionText))
            {
                // Use simple string with default settings
                animatedTextDisplayer.Display(hint.InteractionText);
            }
        }
        else
        {
            // Fallback to static text if animated displayer is not available
            Debug.LogWarning("CrosshairManager: Animated text requested but animatedTextDisplayer is not assigned. Falling back to static text.");
            DisplayStaticText(hint);
        }
    }

    /// <summary>
    /// Hides all text display components.
    /// </summary>
    private void HideAllText()
    {
        // Hide static text
        if (CrosshairText != null)
        {
            CrosshairText.gameObject.SetActive(false);
        }

        // Hide background
        if (CrosshairTextBackground != null)
        {
            CrosshairTextBackground.SetActive(false);
        }

        // Stop animated text
        if (animatedTextDisplayer != null)
        {
            animatedTextDisplayer.StopDisplay();
        }
    }

    #endregion

    #region Public API Extensions

    /// <summary>
    /// Forces the use of static text display regardless of hint settings.
    /// Useful for performance-critical situations.
    /// </summary>
    public void SetInteractableCrosshairStatic(InteractHintData hint)
    {
        if (hint == null)
        {
            SetDefaultCrosshair();
            return;
        }

        UpdateCrosshairSprite(hint);

        if (hint.HasTextContent())
        {
            DisplayStaticText(hint);
        }
        else
        {
            HideAllText();
        }

        isShowingInteractable = true;
    }

    /// <summary>
    /// Forces the use of animated text display regardless of hint settings.
    /// </summary>
    public void SetInteractableCrosshairAnimated(InteractHintData hint)
    {
        if (hint == null)
        {
            SetDefaultCrosshair();
            return;
        }

        UpdateCrosshairSprite(hint);

        if (hint.HasTextContent())
        {
            DisplayAnimatedText(hint);
        }
        else
        {
            HideAllText();
        }

        isShowingInteractable = true;
    }

    /// <summary>
    /// Quick method to display simple animated text without creating a full hint.
    /// </summary>
    public void DisplayQuickAnimatedText(string text)
    {
        if (animatedTextDisplayer != null && !string.IsNullOrEmpty(text))
        {
            if (CrosshairText != null)
            {
                CrosshairText.gameObject.SetActive(false);
            }

            if (CrosshairTextBackground != null)
            {
                CrosshairTextBackground.SetActive(true);
            }

            animatedTextDisplayer.Display(text);
            isShowingInteractable = true;
        }
    }

    #endregion
}
